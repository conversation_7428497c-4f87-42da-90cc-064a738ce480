"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("middleware",{

/***/ "(middleware)/./middleware.ts":
/*!***********************!*\
  !*** ./middleware.ts ***!
  \***********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   middleware: () => (/* binding */ middleware)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(middleware)/./node_modules/next/dist/esm/api/server.js\");\n/* harmony import */ var next_auth_jwt__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/jwt */ \"(middleware)/./node_modules/next-auth/jwt/index.js\");\n/* harmony import */ var next_auth_jwt__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth_jwt__WEBPACK_IMPORTED_MODULE_1__);\n\n\n// Define protected routes and their required roles\nconst protectedRoutes = {\n    '/dashboard': [\n        'ADMIN',\n        'MANAGER',\n        'TEACHER',\n        'RECEPTION',\n        'CASHIER',\n        'STUDENT',\n        'ACADEMIC_MANAGER'\n    ],\n    '/dashboard/analytics': [\n        'ADMIN'\n    ],\n    '/dashboard/users': [\n        'ADMIN'\n    ],\n    '/dashboard/teachers': [\n        'ADMIN',\n        'MANAGER'\n    ],\n    '/dashboard/students': [\n        'ADMIN',\n        'MANAGER',\n        'TEACHER',\n        'RECEPTION',\n        'CASHIER'\n    ],\n    '/dashboard/groups': [\n        'ADMIN',\n        'MANAGER',\n        'TEACHER'\n    ],\n    '/dashboard/enrollments': [\n        'ADMIN',\n        'MANAGER',\n        'RECEPTION'\n    ],\n    '/dashboard/payments': [\n        'ADMIN',\n        'CASHIER'\n    ],\n    '/dashboard/attendance': [\n        'MANAGER',\n        'TEACHER'\n    ],\n    '/dashboard/assessments': [\n        'ADMIN',\n        'MANAGER',\n        'TEACHER',\n        'ACADEMIC_MANAGER'\n    ],\n    '/dashboard/classes': [\n        'ADMIN',\n        'MANAGER',\n        'TEACHER'\n    ],\n    '/dashboard/leads': [\n        'ADMIN',\n        'MANAGER',\n        'RECEPTION'\n    ],\n    '/dashboard/communication': [\n        'ADMIN',\n        'MANAGER',\n        'TEACHER',\n        'RECEPTION'\n    ]\n};\n// API routes that require authentication\nconst protectedApiRoutes = {\n    '/api/analytics': [\n        'ADMIN'\n    ],\n    '/api/reports': [\n        'ADMIN'\n    ],\n    '/api/users': [\n        'ADMIN'\n    ],\n    '/api/teachers': [\n        'ADMIN',\n        'MANAGER'\n    ],\n    '/api/students': [\n        'ADMIN',\n        'MANAGER',\n        'TEACHER',\n        'RECEPTION',\n        'CASHIER',\n        'STUDENT',\n        'ACADEMIC_MANAGER'\n    ],\n    '/api/groups': [\n        'ADMIN',\n        'MANAGER',\n        'TEACHER'\n    ],\n    '/api/enrollments': [\n        'ADMIN',\n        'MANAGER',\n        'RECEPTION'\n    ],\n    '/api/payments': [\n        'ADMIN',\n        'CASHIER'\n    ],\n    '/api/attendance': [\n        'MANAGER',\n        'TEACHER'\n    ],\n    '/api/assessments': [\n        'ADMIN',\n        'MANAGER',\n        'TEACHER',\n        'ACADEMIC_MANAGER'\n    ],\n    '/api/leads': [\n        'ADMIN',\n        'MANAGER',\n        'RECEPTION'\n    ],\n    '/api/courses': [\n        'ADMIN',\n        'MANAGER'\n    ]\n};\n// Public routes that don't require authentication\nconst publicRoutes = [\n    '/',\n    '/auth/signin',\n    '/auth/signup',\n    '/auth/error',\n    '/api/auth',\n    '/api/health',\n    '/api/leads',\n    '/api/auth/verify'\n];\n// Specific inter-server routes (more restrictive than wildcard)\nconst interServerRoutes = [\n    '/api/inter-server/health',\n    '/api/inter-server/auth/validate',\n    '/api/inter-server/users'\n];\nasync function middleware(request) {\n    const { pathname } = request.nextUrl;\n    // Skip middleware for static files and Next.js internals\n    if (pathname.startsWith('/_next') || pathname.startsWith('/static') || pathname.includes('.') || pathname.startsWith('/favicon')) {\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.next();\n    }\n    // Check if route is public\n    const isPublicRoute = publicRoutes.some((route)=>{\n        if (route === pathname) return true;\n        if (route.endsWith('*') && pathname.startsWith(route.slice(0, -1))) return true;\n        return false;\n    });\n    // Check if route is an allowed inter-server route\n    const isInterServerRoute = interServerRoutes.includes(pathname);\n    // Allow public routes and specific inter-server routes\n    if (isPublicRoute || isInterServerRoute) {\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.next();\n    }\n    // Get the token from the request\n    const token = await (0,next_auth_jwt__WEBPACK_IMPORTED_MODULE_1__.getToken)({\n        req: request,\n        secret: process.env.NEXTAUTH_SECRET\n    });\n    // Redirect to signin if no token\n    if (!token) {\n        const signInUrl = new URL('/auth/signin', request.url);\n        signInUrl.searchParams.set('callbackUrl', pathname);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.redirect(signInUrl);\n    }\n    // Check role-based access for protected routes\n    const userRole = token.role;\n    // Admin server: Only allow ADMIN and CASHIER roles\n    const serverType = process.env.SERVER_TYPE || 'admin';\n    if (serverType === 'admin') {\n        const allowedRoles = [\n            'ADMIN',\n            'CASHIER'\n        ];\n        if (!allowedRoles.includes(userRole)) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.redirect(new URL('/auth/signin?error=unauthorized', request.url));\n        }\n    }\n    // Check dashboard routes\n    for (const [route, allowedRoles] of Object.entries(protectedRoutes)){\n        if (pathname.startsWith(route)) {\n            if (!allowedRoles.includes(userRole)) {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.redirect(new URL('/dashboard/unauthorized', request.url));\n            }\n            break;\n        }\n    }\n    // Check API routes\n    for (const [route, allowedRoles] of Object.entries(protectedApiRoutes)){\n        if (pathname.startsWith(route)) {\n            if (!allowedRoles.includes(userRole)) {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: 'Unauthorized access'\n                }, {\n                    status: 403\n                });\n            }\n            break;\n        }\n    }\n    // Special handling for student access\n    if (userRole === 'STUDENT') {\n        // Students can only access their own data\n        const userId = token.sub;\n        // Allow access to student dashboard\n        if (pathname.startsWith('/dashboard/student')) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.next();\n        }\n        // Restrict access to other dashboard routes\n        if (pathname.startsWith('/dashboard') && pathname !== '/dashboard') {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.redirect(new URL('/dashboard/student', request.url));\n        }\n    }\n    // Special handling for academic manager access\n    if (userRole === 'ACADEMIC_MANAGER') {\n        // Academic managers have access to assessments and test statistics\n        const allowedPaths = [\n            '/dashboard',\n            '/dashboard/assessments',\n            '/dashboard/students'\n        ];\n        const isAllowed = allowedPaths.some((path)=>pathname.startsWith(path));\n        if (!isAllowed && pathname.startsWith('/dashboard')) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.redirect(new URL('/dashboard/assessments', request.url));\n        }\n    }\n    // Teacher-specific restrictions\n    if (userRole === 'TEACHER') {\n        // Teachers can access their assigned groups and students\n        if (pathname.startsWith('/dashboard/teacher')) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.next();\n        }\n    }\n    // Reception-specific restrictions\n    if (userRole === 'RECEPTION') {\n        // Reception can access leads, students, and enrollments\n        const allowedPaths = [\n            '/dashboard',\n            '/dashboard/leads',\n            '/dashboard/students',\n            '/dashboard/enrollments'\n        ];\n        if (!allowedPaths.some((path)=>pathname.startsWith(path))) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.redirect(new URL('/dashboard', request.url));\n        }\n    }\n    // Cashier-specific restrictions\n    if (userRole === 'CASHIER') {\n        // Cashiers can ONLY access payments and basic student info - NO financial analytics\n        const allowedPaths = [\n            '/dashboard',\n            '/dashboard/payments',\n            '/dashboard/students'\n        ];\n        if (!allowedPaths.some((path)=>pathname.startsWith(path))) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.redirect(new URL('/dashboard', request.url));\n        }\n        // Block access to any financial analytics or reports\n        if (pathname.includes('/analytics') || pathname.includes('/reports')) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.redirect(new URL('/dashboard/unauthorized', request.url));\n        }\n    }\n    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.next();\n}\nconst config = {\n    matcher: [\n        /*\r\n     * Match all request paths except for the ones starting with:\r\n     * - api/auth (NextAuth.js routes)\r\n     * - _next/static (static files)\r\n     * - _next/image (image optimization files)\r\n     * - favicon.ico (favicon file)\r\n     * - public folder\r\n     */ '/((?!api/auth|_next/static|_next/image|favicon.ico|public).*)'\n    ]\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(middleware)/./middleware.ts\n");

/***/ })

});