"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("middleware",{

/***/ "(middleware)/./middleware.ts":
/*!***********************!*\
  !*** ./middleware.ts ***!
  \***********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   middleware: () => (/* binding */ middleware)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(middleware)/./node_modules/next/dist/esm/api/server.js\");\n/* harmony import */ var next_auth_jwt__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/jwt */ \"(middleware)/./node_modules/next-auth/jwt/index.js\");\n/* harmony import */ var next_auth_jwt__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth_jwt__WEBPACK_IMPORTED_MODULE_1__);\n\n\n// Define protected routes and their required roles\nconst protectedRoutes = {\n    '/dashboard': [\n        'ADMIN',\n        'MANAGER',\n        'TEACHER',\n        'RECEPTION',\n        'CASHIER',\n        'STUDENT',\n        'ACADEMIC_MANAGER'\n    ],\n    '/dashboard/analytics': [\n        'ADMIN'\n    ],\n    '/dashboard/users': [\n        'ADMIN'\n    ],\n    '/dashboard/teachers': [\n        'ADMIN',\n        'MANAGER'\n    ],\n    '/dashboard/students': [\n        'ADMIN',\n        'MANAGER',\n        'TEACHER',\n        'RECEPTION',\n        'CASHIER'\n    ],\n    '/dashboard/groups': [\n        'ADMIN',\n        'MANAGER',\n        'TEACHER'\n    ],\n    '/dashboard/enrollments': [\n        'ADMIN',\n        'MANAGER',\n        'RECEPTION'\n    ],\n    '/dashboard/payments': [\n        'ADMIN',\n        'CASHIER'\n    ],\n    '/dashboard/attendance': [\n        'MANAGER',\n        'TEACHER'\n    ],\n    '/dashboard/assessments': [\n        'ADMIN',\n        'MANAGER',\n        'TEACHER',\n        'ACADEMIC_MANAGER'\n    ],\n    '/dashboard/classes': [\n        'ADMIN',\n        'MANAGER',\n        'TEACHER'\n    ],\n    '/dashboard/leads': [\n        'ADMIN',\n        'MANAGER',\n        'RECEPTION'\n    ],\n    '/dashboard/communication': [\n        'ADMIN',\n        'MANAGER',\n        'TEACHER',\n        'RECEPTION'\n    ]\n};\n// API routes that require authentication\nconst protectedApiRoutes = {\n    '/api/analytics': [\n        'ADMIN'\n    ],\n    '/api/reports': [\n        'ADMIN'\n    ],\n    '/api/users': [\n        'ADMIN'\n    ],\n    '/api/teachers': [\n        'ADMIN',\n        'MANAGER'\n    ],\n    '/api/students': [\n        'ADMIN',\n        'MANAGER',\n        'TEACHER',\n        'RECEPTION',\n        'CASHIER',\n        'STUDENT',\n        'ACADEMIC_MANAGER'\n    ],\n    '/api/groups': [\n        'ADMIN',\n        'MANAGER',\n        'TEACHER'\n    ],\n    '/api/enrollments': [\n        'ADMIN',\n        'MANAGER',\n        'RECEPTION'\n    ],\n    '/api/payments': [\n        'ADMIN',\n        'CASHIER'\n    ],\n    '/api/attendance': [\n        'MANAGER',\n        'TEACHER'\n    ],\n    '/api/assessments': [\n        'ADMIN',\n        'MANAGER',\n        'TEACHER',\n        'ACADEMIC_MANAGER'\n    ],\n    '/api/leads': [\n        'ADMIN',\n        'MANAGER',\n        'RECEPTION'\n    ],\n    '/api/courses': [\n        'ADMIN',\n        'MANAGER'\n    ]\n};\n// Public routes that don't require authentication\nconst publicRoutes = [\n    '/',\n    '/auth/signin',\n    '/auth/signup',\n    '/auth/error',\n    '/api/auth',\n    '/api/health',\n    '/api/leads',\n    '/api/auth/verify',\n    '/api/inter-server*'\n];\nasync function middleware(request) {\n    const { pathname } = request.nextUrl;\n    // Skip middleware for static files and Next.js internals\n    if (pathname.startsWith('/_next') || pathname.startsWith('/static') || pathname.includes('.') || pathname.startsWith('/favicon')) {\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.next();\n    }\n    // Check if route is public\n    const isPublicRoute = publicRoutes.some((route)=>{\n        if (route === pathname) return true;\n        if (route.endsWith('*') && pathname.startsWith(route.slice(0, -1))) return true;\n        return false;\n    });\n    // Allow public routes\n    if (isPublicRoute) {\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.next();\n    }\n    // Get the token from the request\n    const token = await (0,next_auth_jwt__WEBPACK_IMPORTED_MODULE_1__.getToken)({\n        req: request,\n        secret: process.env.NEXTAUTH_SECRET\n    });\n    // Redirect to signin if no token\n    if (!token) {\n        const signInUrl = new URL('/auth/signin', request.url);\n        signInUrl.searchParams.set('callbackUrl', pathname);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.redirect(signInUrl);\n    }\n    // Check role-based access for protected routes\n    const userRole = token.role;\n    // Admin server: Only allow ADMIN and CASHIER roles\n    const serverType = process.env.SERVER_TYPE || 'admin';\n    if (serverType === 'admin') {\n        const allowedRoles = [\n            'ADMIN',\n            'CASHIER'\n        ];\n        if (!allowedRoles.includes(userRole)) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.redirect(new URL('/auth/signin?error=unauthorized', request.url));\n        }\n    }\n    // Check dashboard routes\n    for (const [route, allowedRoles] of Object.entries(protectedRoutes)){\n        if (pathname.startsWith(route)) {\n            if (!allowedRoles.includes(userRole)) {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.redirect(new URL('/dashboard/unauthorized', request.url));\n            }\n            break;\n        }\n    }\n    // Check API routes\n    for (const [route, allowedRoles] of Object.entries(protectedApiRoutes)){\n        if (pathname.startsWith(route)) {\n            if (!allowedRoles.includes(userRole)) {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: 'Unauthorized access'\n                }, {\n                    status: 403\n                });\n            }\n            break;\n        }\n    }\n    // Special handling for student access\n    if (userRole === 'STUDENT') {\n        // Students can only access their own data\n        const userId = token.sub;\n        // Allow access to student dashboard\n        if (pathname.startsWith('/dashboard/student')) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.next();\n        }\n        // Restrict access to other dashboard routes\n        if (pathname.startsWith('/dashboard') && pathname !== '/dashboard') {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.redirect(new URL('/dashboard/student', request.url));\n        }\n    }\n    // Special handling for academic manager access\n    if (userRole === 'ACADEMIC_MANAGER') {\n        // Academic managers have access to assessments and test statistics\n        const allowedPaths = [\n            '/dashboard',\n            '/dashboard/assessments',\n            '/dashboard/students'\n        ];\n        const isAllowed = allowedPaths.some((path)=>pathname.startsWith(path));\n        if (!isAllowed && pathname.startsWith('/dashboard')) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.redirect(new URL('/dashboard/assessments', request.url));\n        }\n    }\n    // Teacher-specific restrictions\n    if (userRole === 'TEACHER') {\n        // Teachers can access their assigned groups and students\n        if (pathname.startsWith('/dashboard/teacher')) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.next();\n        }\n    }\n    // Reception-specific restrictions\n    if (userRole === 'RECEPTION') {\n        // Reception can access leads, students, and enrollments\n        const allowedPaths = [\n            '/dashboard',\n            '/dashboard/leads',\n            '/dashboard/students',\n            '/dashboard/enrollments'\n        ];\n        if (!allowedPaths.some((path)=>pathname.startsWith(path))) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.redirect(new URL('/dashboard', request.url));\n        }\n    }\n    // Cashier-specific restrictions\n    if (userRole === 'CASHIER') {\n        // Cashiers can ONLY access payments and basic student info - NO financial analytics\n        const allowedPaths = [\n            '/dashboard',\n            '/dashboard/payments',\n            '/dashboard/students'\n        ];\n        if (!allowedPaths.some((path)=>pathname.startsWith(path))) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.redirect(new URL('/dashboard', request.url));\n        }\n        // Block access to any financial analytics or reports\n        if (pathname.includes('/analytics') || pathname.includes('/reports')) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.redirect(new URL('/dashboard/unauthorized', request.url));\n        }\n    }\n    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.next();\n}\nconst config = {\n    matcher: [\n        /*\r\n     * Match all request paths except for the ones starting with:\r\n     * - api/auth (NextAuth.js routes)\r\n     * - _next/static (static files)\r\n     * - _next/image (image optimization files)\r\n     * - favicon.ico (favicon file)\r\n     * - public folder\r\n     */ '/((?!api/auth|_next/static|_next/image|favicon.ico|public).*)'\n    ]\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(middleware)/./middleware.ts\n");

/***/ })

});