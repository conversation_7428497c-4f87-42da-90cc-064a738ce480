/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/notifications/test/route";
exports.ids = ["app/api/notifications/test/route"];
exports.modules = {

/***/ "(rsc)/./app/api/notifications/test/route.ts":
/*!*********************************************!*\
  !*** ./app/api/notifications/test/route.ts ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n\n// Test endpoint for notifications that doesn't require database\nasync function GET(request) {\n    try {\n        const { searchParams } = new URL(request.url);\n        const action = searchParams.get('action');\n        if (action === 'mock') {\n            // Return mock notifications for testing\n            const mockNotifications = [\n                {\n                    id: '1',\n                    title: 'New Student Enrollment',\n                    message: 'John Doe has enrolled in IELTS course',\n                    type: 'success',\n                    priority: 'medium',\n                    read: false,\n                    createdAt: new Date(Date.now() - 1000 * 60 * 30).toISOString(),\n                    actionUrl: '/dashboard/students'\n                },\n                {\n                    id: '2',\n                    title: 'Payment Received',\n                    message: 'Payment of $500 received from Sarah Smith',\n                    type: 'success',\n                    priority: 'medium',\n                    read: false,\n                    createdAt: new Date(Date.now() - 1000 * 60 * 60 * 2).toISOString(),\n                    actionUrl: '/dashboard/payments'\n                },\n                {\n                    id: '3',\n                    title: 'Class Reminder',\n                    message: 'B2 class starts in 30 minutes',\n                    type: 'info',\n                    priority: 'high',\n                    read: true,\n                    createdAt: new Date(Date.now() - 1000 * 60 * 60 * 4).toISOString(),\n                    actionUrl: '/dashboard/classes'\n                },\n                {\n                    id: '4',\n                    title: 'Low Attendance Alert',\n                    message: 'Student Mike Johnson has missed 3 consecutive classes',\n                    type: 'warning',\n                    priority: 'high',\n                    read: false,\n                    createdAt: new Date(Date.now() - 1000 * 60 * 60 * 6).toISOString(),\n                    actionUrl: '/dashboard/attendance'\n                },\n                {\n                    id: '5',\n                    title: 'System Maintenance',\n                    message: 'Scheduled maintenance tonight at 2 AM',\n                    type: 'info',\n                    priority: 'low',\n                    read: true,\n                    createdAt: new Date(Date.now() - 1000 * 60 * 60 * 24).toISOString()\n                }\n            ];\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                notifications: mockNotifications,\n                unreadCount: mockNotifications.filter((n)=>!n.read).length,\n                total: mockNotifications.length\n            });\n        }\n        if (action === 'status') {\n            // Return notification system status\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                status: 'operational',\n                services: {\n                    sms: {\n                        available: true,\n                        provider: 'mock'\n                    },\n                    email: {\n                        available: true,\n                        provider: 'mock'\n                    },\n                    push: {\n                        available: false,\n                        provider: 'none'\n                    }\n                },\n                stats: {\n                    totalSent: 1247,\n                    sentToday: 23,\n                    failureRate: 0.02\n                }\n            });\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            message: 'Notification test endpoint',\n            availableActions: [\n                'mock',\n                'status'\n            ],\n            usage: {\n                mock: 'GET /api/notifications/test?action=mock - Returns mock notifications',\n                status: 'GET /api/notifications/test?action=status - Returns system status'\n            }\n        });\n    } catch (error) {\n        console.error('Error in notification test endpoint:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Internal server error'\n        }, {\n            status: 500\n        });\n    }\n}\nasync function POST(request) {\n    try {\n        const body = await request.json();\n        // Mock notification sending\n        console.log('Mock notification sent:', body);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            message: 'Mock notification sent successfully',\n            data: {\n                id: `mock-${Date.now()}`,\n                sentAt: new Date().toISOString(),\n                channels: body.channels || [\n                    'mock'\n                ],\n                recipient: body.recipientId || 'test-user'\n            }\n        });\n    } catch (error) {\n        console.error('Error in mock notification sending:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Failed to send mock notification'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/notifications/test/route.ts\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fnotifications%2Ftest%2Froute&page=%2Fapi%2Fnotifications%2Ftest%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fnotifications%2Ftest%2Froute.ts&appDir=C%3A%5CUsers%5CWindows%2011%5CDesktop%5Ccodes%5Cinno-crm%5Cinno-crm-admin%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CWindows%2011%5CDesktop%5Ccodes%5Cinno-crm%5Cinno-crm-admin&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fnotifications%2Ftest%2Froute&page=%2Fapi%2Fnotifications%2Ftest%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fnotifications%2Ftest%2Froute.ts&appDir=C%3A%5CUsers%5CWindows%2011%5CDesktop%5Ccodes%5Cinno-crm%5Cinno-crm-admin%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CWindows%2011%5CDesktop%5Ccodes%5Cinno-crm%5Cinno-crm-admin&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_Windows_11_Desktop_codes_inno_crm_inno_crm_admin_app_api_notifications_test_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/notifications/test/route.ts */ \"(rsc)/./app/api/notifications/test/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/notifications/test/route\",\n        pathname: \"/api/notifications/test\",\n        filename: \"route\",\n        bundlePath: \"app/api/notifications/test/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\inno-crm\\\\inno-crm-admin\\\\app\\\\api\\\\notifications\\\\test\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_Windows_11_Desktop_codes_inno_crm_inno_crm_admin_app_api_notifications_test_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fnotifications%2Ftest%2Froute&page=%2Fapi%2Fnotifications%2Ftest%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fnotifications%2Ftest%2Froute.ts&appDir=C%3A%5CUsers%5CWindows%2011%5CDesktop%5Ccodes%5Cinno-crm%5Cinno-crm-admin%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CWindows%2011%5CDesktop%5Ccodes%5Cinno-crm%5Cinno-crm-admin&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fnotifications%2Ftest%2Froute&page=%2Fapi%2Fnotifications%2Ftest%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fnotifications%2Ftest%2Froute.ts&appDir=C%3A%5CUsers%5CWindows%2011%5CDesktop%5Ccodes%5Cinno-crm%5Cinno-crm-admin%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CWindows%2011%5CDesktop%5Ccodes%5Cinno-crm%5Cinno-crm-admin&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();