"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("middleware",{

/***/ "(middleware)/./middleware.ts":
/*!***********************!*\
  !*** ./middleware.ts ***!
  \***********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   middleware: () => (/* binding */ middleware)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(middleware)/./node_modules/next/dist/esm/api/server.js\");\n/* harmony import */ var next_auth_jwt__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/jwt */ \"(middleware)/./node_modules/next-auth/jwt/index.js\");\n/* harmony import */ var next_auth_jwt__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth_jwt__WEBPACK_IMPORTED_MODULE_1__);\n\n\n// Define protected routes and their required roles\nconst protectedRoutes = {\n    '/dashboard': [\n        'ADMIN',\n        'MANAGER',\n        'TEACHER',\n        'RECEPTION',\n        'CASHIER',\n        'STUDENT',\n        'ACADEMIC_MANAGER'\n    ],\n    '/dashboard/analytics': [\n        'ADMIN'\n    ],\n    '/dashboard/users': [\n        'ADMIN'\n    ],\n    '/dashboard/teachers': [\n        'ADMIN',\n        'MANAGER'\n    ],\n    '/dashboard/students': [\n        'ADMIN',\n        'MANAGER',\n        'TEACHER',\n        'RECEPTION',\n        'CASHIER'\n    ],\n    '/dashboard/groups': [\n        'ADMIN',\n        'MANAGER',\n        'TEACHER'\n    ],\n    '/dashboard/enrollments': [\n        'ADMIN',\n        'MANAGER',\n        'RECEPTION'\n    ],\n    '/dashboard/payments': [\n        'ADMIN',\n        'CASHIER'\n    ],\n    '/dashboard/attendance': [\n        'MANAGER',\n        'TEACHER'\n    ],\n    '/dashboard/assessments': [\n        'ADMIN',\n        'MANAGER',\n        'TEACHER',\n        'ACADEMIC_MANAGER'\n    ],\n    '/dashboard/classes': [\n        'ADMIN',\n        'MANAGER',\n        'TEACHER'\n    ],\n    '/dashboard/leads': [\n        'ADMIN',\n        'MANAGER',\n        'RECEPTION'\n    ],\n    '/dashboard/communication': [\n        'ADMIN',\n        'MANAGER',\n        'TEACHER',\n        'RECEPTION'\n    ]\n};\n// API routes that require authentication\nconst protectedApiRoutes = {\n    '/api/analytics': [\n        'ADMIN'\n    ],\n    '/api/reports': [\n        'ADMIN'\n    ],\n    '/api/users': [\n        'ADMIN'\n    ],\n    '/api/teachers': [\n        'ADMIN',\n        'MANAGER'\n    ],\n    '/api/students': [\n        'ADMIN',\n        'MANAGER',\n        'TEACHER',\n        'RECEPTION',\n        'CASHIER',\n        'STUDENT',\n        'ACADEMIC_MANAGER'\n    ],\n    '/api/groups': [\n        'ADMIN',\n        'MANAGER',\n        'TEACHER'\n    ],\n    '/api/enrollments': [\n        'ADMIN',\n        'MANAGER',\n        'RECEPTION'\n    ],\n    '/api/payments': [\n        'ADMIN',\n        'CASHIER'\n    ],\n    '/api/attendance': [\n        'MANAGER',\n        'TEACHER'\n    ],\n    '/api/assessments': [\n        'ADMIN',\n        'MANAGER',\n        'TEACHER',\n        'ACADEMIC_MANAGER'\n    ],\n    '/api/leads': [\n        'ADMIN',\n        'MANAGER',\n        'RECEPTION'\n    ],\n    '/api/courses': [\n        'ADMIN',\n        'MANAGER'\n    ]\n};\n// Public routes that don't require authentication\nconst publicRoutes = [\n    '/',\n    '/auth/signin',\n    '/auth/signup',\n    '/auth/error',\n    '/api/auth',\n    '/api/health',\n    '/api/leads',\n    '/api/auth/verify'\n];\n// Specific inter-server routes (more restrictive than wildcard)\nconst interServerRoutes = [\n    '/api/inter-server/health',\n    '/api/inter-server/auth/validate',\n    '/api/inter-server/users'\n];\nasync function middleware(request) {\n    const { pathname } = request.nextUrl;\n    // Skip middleware for static files and Next.js internals\n    if (pathname.startsWith('/_next') || pathname.startsWith('/static') || pathname.includes('.') || pathname.startsWith('/favicon')) {\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.next();\n    }\n    // Check if route is public\n    const isPublicRoute = publicRoutes.some((route)=>{\n        if (route === pathname) return true;\n        if (route.endsWith('*') && pathname.startsWith(route.slice(0, -1))) return true;\n        return false;\n    });\n    // Allow public routes\n    if (isPublicRoute) {\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.next();\n    }\n    // Get the token from the request\n    const token = await (0,next_auth_jwt__WEBPACK_IMPORTED_MODULE_1__.getToken)({\n        req: request,\n        secret: process.env.NEXTAUTH_SECRET\n    });\n    // Redirect to signin if no token\n    if (!token) {\n        const signInUrl = new URL('/auth/signin', request.url);\n        signInUrl.searchParams.set('callbackUrl', pathname);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.redirect(signInUrl);\n    }\n    // Check role-based access for protected routes\n    const userRole = token.role;\n    // Admin server: Only allow ADMIN and CASHIER roles\n    const serverType = process.env.SERVER_TYPE || 'admin';\n    if (serverType === 'admin') {\n        const allowedRoles = [\n            'ADMIN',\n            'CASHIER'\n        ];\n        if (!allowedRoles.includes(userRole)) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.redirect(new URL('/auth/signin?error=unauthorized', request.url));\n        }\n    }\n    // Check dashboard routes\n    for (const [route, allowedRoles] of Object.entries(protectedRoutes)){\n        if (pathname.startsWith(route)) {\n            if (!allowedRoles.includes(userRole)) {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.redirect(new URL('/dashboard/unauthorized', request.url));\n            }\n            break;\n        }\n    }\n    // Check API routes\n    for (const [route, allowedRoles] of Object.entries(protectedApiRoutes)){\n        if (pathname.startsWith(route)) {\n            if (!allowedRoles.includes(userRole)) {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: 'Unauthorized access'\n                }, {\n                    status: 403\n                });\n            }\n            break;\n        }\n    }\n    // Special handling for student access\n    if (userRole === 'STUDENT') {\n        // Students can only access their own data\n        const userId = token.sub;\n        // Allow access to student dashboard\n        if (pathname.startsWith('/dashboard/student')) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.next();\n        }\n        // Restrict access to other dashboard routes\n        if (pathname.startsWith('/dashboard') && pathname !== '/dashboard') {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.redirect(new URL('/dashboard/student', request.url));\n        }\n    }\n    // Special handling for academic manager access\n    if (userRole === 'ACADEMIC_MANAGER') {\n        // Academic managers have access to assessments and test statistics\n        const allowedPaths = [\n            '/dashboard',\n            '/dashboard/assessments',\n            '/dashboard/students'\n        ];\n        const isAllowed = allowedPaths.some((path)=>pathname.startsWith(path));\n        if (!isAllowed && pathname.startsWith('/dashboard')) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.redirect(new URL('/dashboard/assessments', request.url));\n        }\n    }\n    // Teacher-specific restrictions\n    if (userRole === 'TEACHER') {\n        // Teachers can access their assigned groups and students\n        if (pathname.startsWith('/dashboard/teacher')) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.next();\n        }\n    }\n    // Reception-specific restrictions\n    if (userRole === 'RECEPTION') {\n        // Reception can access leads, students, and enrollments\n        const allowedPaths = [\n            '/dashboard',\n            '/dashboard/leads',\n            '/dashboard/students',\n            '/dashboard/enrollments'\n        ];\n        if (!allowedPaths.some((path)=>pathname.startsWith(path))) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.redirect(new URL('/dashboard', request.url));\n        }\n    }\n    // Cashier-specific restrictions\n    if (userRole === 'CASHIER') {\n        // Cashiers can ONLY access payments and basic student info - NO financial analytics\n        const allowedPaths = [\n            '/dashboard',\n            '/dashboard/payments',\n            '/dashboard/students'\n        ];\n        if (!allowedPaths.some((path)=>pathname.startsWith(path))) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.redirect(new URL('/dashboard', request.url));\n        }\n        // Block access to any financial analytics or reports\n        if (pathname.includes('/analytics') || pathname.includes('/reports')) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.redirect(new URL('/dashboard/unauthorized', request.url));\n        }\n    }\n    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.next();\n}\nconst config = {\n    matcher: [\n        /*\r\n     * Match all request paths except for the ones starting with:\r\n     * - api/auth (NextAuth.js routes)\r\n     * - _next/static (static files)\r\n     * - _next/image (image optimization files)\r\n     * - favicon.ico (favicon file)\r\n     * - public folder\r\n     */ '/((?!api/auth|_next/static|_next/image|favicon.ico|public).*)'\n    ]\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(middleware)/./middleware.ts\n");

/***/ })

});