/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/dashboard/stats/route";
exports.ids = ["app/api/dashboard/stats/route"];
exports.modules = {

/***/ "(rsc)/./app/api/dashboard/stats/route.ts":
/*!******************************************!*\
  !*** ./app/api/dashboard/stats/route.ts ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth */ \"(rsc)/./node_modules/next-auth/index.js\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/auth */ \"(rsc)/./lib/auth.ts\");\n/* harmony import */ var _lib_prisma__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/prisma */ \"(rsc)/./lib/prisma.ts\");\n\n\n\n\nasync function GET(request) {\n    try {\n        const session = await (0,next_auth__WEBPACK_IMPORTED_MODULE_1__.getServerSession)(_lib_auth__WEBPACK_IMPORTED_MODULE_2__.authOptions);\n        if (!session?.user) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Unauthorized'\n            }, {\n                status: 401\n            });\n        }\n        // Get current date ranges\n        const now = new Date();\n        const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);\n        const startOfLastMonth = new Date(now.getFullYear(), now.getMonth() - 1, 1);\n        const endOfLastMonth = new Date(now.getFullYear(), now.getMonth(), 0);\n        const startOfWeek = new Date(now);\n        startOfWeek.setDate(now.getDate() - 7);\n        // Get total students count (without status filter for now)\n        const totalStudents = await _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.student.count();\n        // Get last month's student count for comparison\n        const lastMonthStudents = await _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.student.count({\n            where: {\n                createdAt: {\n                    lte: endOfLastMonth\n                }\n            }\n        });\n        // Calculate student growth percentage\n        const studentGrowth = lastMonthStudents > 0 ? Math.round((totalStudents - lastMonthStudents) / lastMonthStudents * 100) : 0;\n        // Get new leads this week (with error handling)\n        let newLeads = 0;\n        let lastWeekLeads = 0;\n        try {\n            newLeads = await _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.lead.count({\n                where: {\n                    createdAt: {\n                        gte: startOfWeek\n                    }\n                }\n            });\n            // Get last week's leads for comparison\n            const lastWeekStart = new Date(startOfWeek);\n            lastWeekStart.setDate(lastWeekStart.getDate() - 7);\n            lastWeekLeads = await _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.lead.count({\n                where: {\n                    createdAt: {\n                        gte: lastWeekStart,\n                        lt: startOfWeek\n                    }\n                }\n            });\n        } catch (error) {\n            console.log('Leads table not available:', error);\n        }\n        // Calculate leads growth percentage\n        const leadsGrowth = lastWeekLeads > 0 ? Math.round((newLeads - lastWeekLeads) / lastWeekLeads * 100) : 0;\n        // Get active groups count (with error handling)\n        let activeGroups = 0;\n        try {\n            activeGroups = await _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.group.count({\n                where: {\n                    isActive: true\n                }\n            });\n        } catch (error) {\n            console.log('Groups table not available:', error);\n        }\n        // Get monthly revenue (with error handling)\n        let currentRevenue = 0;\n        let previousRevenue = 0;\n        try {\n            const monthlyRevenue = await _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.payment.aggregate({\n                where: {\n                    status: 'PAID',\n                    createdAt: {\n                        gte: startOfMonth,\n                        lte: now\n                    }\n                },\n                _sum: {\n                    amount: true\n                }\n            });\n            // Get last month's revenue for comparison\n            const lastMonthRevenue = await _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.payment.aggregate({\n                where: {\n                    status: 'PAID',\n                    createdAt: {\n                        gte: startOfLastMonth,\n                        lte: endOfLastMonth\n                    }\n                },\n                _sum: {\n                    amount: true\n                }\n            });\n            currentRevenue = Number(monthlyRevenue._sum.amount) || 0;\n            previousRevenue = Number(lastMonthRevenue._sum.amount) || 0;\n        } catch (error) {\n            console.log('Payments table not available:', error);\n        }\n        // Calculate revenue growth percentage\n        const revenueGrowth = previousRevenue > 0 ? Math.round((currentRevenue - previousRevenue) / previousRevenue * 100) : 0;\n        // Get recent leads (last 4) with error handling\n        let recentLeads = [];\n        try {\n            recentLeads = await _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.lead.findMany({\n                orderBy: {\n                    createdAt: 'desc'\n                },\n                take: 4,\n                select: {\n                    id: true,\n                    name: true,\n                    coursePreference: true,\n                    createdAt: true\n                }\n            });\n        } catch (error) {\n            console.log('Recent leads not available:', error);\n        }\n        // Get upcoming classes for today (with error handling)\n        let upcomingClasses = [];\n        try {\n            const today = new Date();\n            today.setHours(0, 0, 0, 0);\n            const tomorrow = new Date(today);\n            tomorrow.setDate(tomorrow.getDate() + 1);\n            upcomingClasses = await _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.class.findMany({\n                where: {\n                    date: {\n                        gte: today,\n                        lt: tomorrow\n                    }\n                },\n                include: {\n                    group: {\n                        include: {\n                            course: {\n                                select: {\n                                    name: true,\n                                    level: true\n                                }\n                            },\n                            teacher: {\n                                include: {\n                                    user: {\n                                        select: {\n                                            name: true\n                                        }\n                                    }\n                                }\n                            }\n                        }\n                    }\n                },\n                orderBy: {\n                    date: 'asc'\n                },\n                take: 4\n            });\n        } catch (error) {\n            console.log('Classes table not available:', error);\n        }\n        // Format the response\n        const stats = {\n            totalStudents: {\n                count: totalStudents,\n                growth: studentGrowth\n            },\n            newLeads: {\n                count: newLeads,\n                growth: leadsGrowth\n            },\n            activeGroups: {\n                count: activeGroups\n            },\n            monthlyRevenue: {\n                amount: currentRevenue,\n                growth: revenueGrowth\n            },\n            recentLeads: recentLeads.map((lead)=>({\n                    name: lead.name,\n                    course: lead.coursePreference,\n                    status: 'NEW',\n                    time: getTimeAgo(lead.createdAt)\n                })),\n            upcomingClasses: upcomingClasses.map((classItem)=>({\n                    group: `${classItem.group?.course?.name || 'Unknown Course'} - ${classItem.group?.course?.level || 'Unknown Level'}`,\n                    teacher: classItem.group?.teacher?.user?.name || 'No Teacher',\n                    time: classItem.date ? formatTime(classItem.date.toISOString()) : 'TBA',\n                    room: 'TBA'\n                }))\n        };\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(stats);\n    } catch (error) {\n        console.error('Error fetching dashboard stats:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Internal server error'\n        }, {\n            status: 500\n        });\n    }\n}\nfunction getTimeAgo(date) {\n    const now = new Date();\n    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));\n    if (diffInHours < 1) {\n        return 'Just now';\n    } else if (diffInHours < 24) {\n        return `${diffInHours} hour${diffInHours > 1 ? 's' : ''} ago`;\n    } else {\n        const diffInDays = Math.floor(diffInHours / 24);\n        return `${diffInDays} day${diffInDays > 1 ? 's' : ''} ago`;\n    }\n}\nfunction formatTime(dateString) {\n    const date = new Date(dateString);\n    return date.toLocaleTimeString('en-US', {\n        hour: 'numeric',\n        minute: '2-digit',\n        hour12: true\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/dashboard/stats/route.ts\n");

/***/ }),

/***/ "(rsc)/./lib/auth.ts":
/*!*********************!*\
  !*** ./lib/auth.ts ***!
  \*********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authOptions: () => (/* binding */ authOptions)\n/* harmony export */ });\n/* harmony import */ var next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-auth/providers/credentials */ \"(rsc)/./node_modules/next-auth/providers/credentials.js\");\n/* harmony import */ var _lib_prisma__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/prisma */ \"(rsc)/./lib/prisma.ts\");\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! bcryptjs */ \"bcryptjs\");\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(bcryptjs__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\nconst authOptions = {\n    providers: [\n        (0,next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n            name: \"credentials\",\n            credentials: {\n                phone: {\n                    label: \"Phone\",\n                    type: \"text\"\n                },\n                password: {\n                    label: \"Password\",\n                    type: \"password\"\n                }\n            },\n            async authorize (credentials) {\n                if (!credentials?.phone || !credentials?.password) {\n                    return null;\n                }\n                try {\n                    // Admin server: Authenticate directly against database\n                    const user = await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.user.findUnique({\n                        where: {\n                            phone: credentials.phone\n                        },\n                        select: {\n                            id: true,\n                            phone: true,\n                            name: true,\n                            email: true,\n                            role: true,\n                            password: true,\n                            createdAt: true,\n                            updatedAt: true\n                        }\n                    });\n                    if (!user) {\n                        console.error('User not found:', credentials.phone);\n                        return null;\n                    }\n                    // Verify the password\n                    const isPasswordValid = await bcryptjs__WEBPACK_IMPORTED_MODULE_2___default().compare(credentials.password, user.password);\n                    if (!isPasswordValid) {\n                        console.error('Invalid password for user:', credentials.phone);\n                        return null;\n                    }\n                    // Verify the user role is allowed on admin server\n                    const allowedRoles = [\n                        'ADMIN',\n                        'CASHIER'\n                    ];\n                    if (!allowedRoles.includes(user.role)) {\n                        console.error('User role not allowed on admin server:', user.role);\n                        return null;\n                    }\n                    return {\n                        id: user.id,\n                        phone: user.phone,\n                        name: user.name,\n                        email: user.email,\n                        role: user.role\n                    };\n                } catch (error) {\n                    console.error('Error authenticating user:', error);\n                    return null;\n                }\n            }\n        })\n    ],\n    session: {\n        strategy: \"jwt\"\n    },\n    callbacks: {\n        async jwt ({ token, user }) {\n            if (user) {\n                token.role = user.role || null;\n            }\n            return token;\n        },\n        async session ({ session, token }) {\n            if (token) {\n                session.user.id = token.sub;\n                session.user.role = token.role || null;\n            }\n            return session;\n        }\n    },\n    pages: {\n        signIn: \"/auth/signin\"\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/auth.ts\n");

/***/ }),

/***/ "(rsc)/./lib/prisma.ts":
/*!***********************!*\
  !*** ./lib/prisma.ts ***!
  \***********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   prisma: () => (/* binding */ prisma)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n\nconst globalForPrisma = globalThis;\nconst prisma = globalForPrisma.prisma ?? new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient();\nif (true) globalForPrisma.prisma = prisma;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9saWIvcHJpc21hLnRzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUE2QztBQUU3QyxNQUFNQyxrQkFBa0JDO0FBSWpCLE1BQU1DLFNBQVNGLGdCQUFnQkUsTUFBTSxJQUFJLElBQUlILHdEQUFZQSxHQUFFO0FBRWxFLElBQUlJLElBQXFDLEVBQUVILGdCQUFnQkUsTUFBTSxHQUFHQSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxXaW5kb3dzIDExXFxEZXNrdG9wXFxjb2Rlc1xcaW5uby1jcm1cXGlubm8tY3JtLWFkbWluXFxsaWJcXHByaXNtYS50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBQcmlzbWFDbGllbnQgfSBmcm9tICdAcHJpc21hL2NsaWVudCdcclxuXHJcbmNvbnN0IGdsb2JhbEZvclByaXNtYSA9IGdsb2JhbFRoaXMgYXMgdW5rbm93biBhcyB7XHJcbiAgcHJpc21hOiBQcmlzbWFDbGllbnQgfCB1bmRlZmluZWRcclxufVxyXG5cclxuZXhwb3J0IGNvbnN0IHByaXNtYSA9IGdsb2JhbEZvclByaXNtYS5wcmlzbWEgPz8gbmV3IFByaXNtYUNsaWVudCgpXHJcblxyXG5pZiAocHJvY2Vzcy5lbnYuTk9ERV9FTlYgIT09ICdwcm9kdWN0aW9uJykgZ2xvYmFsRm9yUHJpc21hLnByaXNtYSA9IHByaXNtYVxyXG4iXSwibmFtZXMiOlsiUHJpc21hQ2xpZW50IiwiZ2xvYmFsRm9yUHJpc21hIiwiZ2xvYmFsVGhpcyIsInByaXNtYSIsInByb2Nlc3MiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./lib/prisma.ts\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fdashboard%2Fstats%2Froute&page=%2Fapi%2Fdashboard%2Fstats%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fdashboard%2Fstats%2Froute.ts&appDir=C%3A%5CUsers%5CWindows%2011%5CDesktop%5Ccodes%5Cinno-crm%5Cinno-crm-admin%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CWindows%2011%5CDesktop%5Ccodes%5Cinno-crm%5Cinno-crm-admin&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fdashboard%2Fstats%2Froute&page=%2Fapi%2Fdashboard%2Fstats%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fdashboard%2Fstats%2Froute.ts&appDir=C%3A%5CUsers%5CWindows%2011%5CDesktop%5Ccodes%5Cinno-crm%5Cinno-crm-admin%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CWindows%2011%5CDesktop%5Ccodes%5Cinno-crm%5Cinno-crm-admin&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_Windows_11_Desktop_codes_inno_crm_inno_crm_admin_app_api_dashboard_stats_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/dashboard/stats/route.ts */ \"(rsc)/./app/api/dashboard/stats/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/dashboard/stats/route\",\n        pathname: \"/api/dashboard/stats\",\n        filename: \"route\",\n        bundlePath: \"app/api/dashboard/stats/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\inno-crm\\\\inno-crm-admin\\\\app\\\\api\\\\dashboard\\\\stats\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_Windows_11_Desktop_codes_inno_crm_inno_crm_admin_app_api_dashboard_stats_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fdashboard%2Fstats%2Froute&page=%2Fapi%2Fdashboard%2Fstats%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fdashboard%2Fstats%2Froute.ts&appDir=C%3A%5CUsers%5CWindows%2011%5CDesktop%5Ccodes%5Cinno-crm%5Cinno-crm-admin%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CWindows%2011%5CDesktop%5Ccodes%5Cinno-crm%5Cinno-crm-admin&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("@prisma/client");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("assert");

/***/ }),

/***/ "bcryptjs":
/*!***************************!*\
  !*** external "bcryptjs" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("bcryptjs");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "querystring":
/*!******************************!*\
  !*** external "querystring" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("querystring");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/next-auth","vendor-chunks/@babel","vendor-chunks/openid-client","vendor-chunks/oauth","vendor-chunks/preact","vendor-chunks/uuid","vendor-chunks/yallist","vendor-chunks/preact-render-to-string","vendor-chunks/oidc-token-hash","vendor-chunks/@panva"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fdashboard%2Fstats%2Froute&page=%2Fapi%2Fdashboard%2Fstats%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fdashboard%2Fstats%2Froute.ts&appDir=C%3A%5CUsers%5CWindows%2011%5CDesktop%5Ccodes%5Cinno-crm%5Cinno-crm-admin%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CWindows%2011%5CDesktop%5Ccodes%5Cinno-crm%5Cinno-crm-admin&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();