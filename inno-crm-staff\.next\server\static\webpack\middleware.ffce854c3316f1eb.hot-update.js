"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("middleware",{

/***/ "(middleware)/./middleware.ts":
/*!***********************!*\
  !*** ./middleware.ts ***!
  \***********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   middleware: () => (/* binding */ middleware)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(middleware)/./node_modules/next/dist/esm/api/server.js\");\n/* harmony import */ var next_auth_jwt__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/jwt */ \"(middleware)/./node_modules/next-auth/jwt/index.js\");\n/* harmony import */ var next_auth_jwt__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth_jwt__WEBPACK_IMPORTED_MODULE_1__);\n\n\n// Define protected routes and their required roles (Staff Server)\nconst protectedRoutes = {\n    '/dashboard': [\n        'MANAGER',\n        'TEACHER',\n        'RECEPTION',\n        'ACADEMIC_MANAGER'\n    ],\n    '/dashboard/teachers': [\n        'MANAGER'\n    ],\n    '/dashboard/students': [\n        'MANAGER',\n        'TEACHER',\n        'RECEPTION'\n    ],\n    '/dashboard/groups': [\n        'MANAGER',\n        'TEACHER'\n    ],\n    '/dashboard/enrollments': [\n        'MANAGER',\n        'RECEPTION'\n    ],\n    '/dashboard/attendance': [\n        'MANAGER',\n        'TEACHER'\n    ],\n    '/dashboard/assessments': [\n        'MANAGER',\n        'TEACHER',\n        'ACADEMIC_MANAGER'\n    ],\n    '/dashboard/classes': [\n        'MANAGER',\n        'TEACHER'\n    ],\n    '/dashboard/leads': [\n        'MANAGER',\n        'RECEPTION'\n    ],\n    '/dashboard/communication': [\n        'MANAGER',\n        'TEACHER',\n        'RECEPTION'\n    ]\n};\n// API routes that require authentication (Staff Server)\nconst protectedApiRoutes = {\n    '/api/teachers': [\n        'MANAGER'\n    ],\n    '/api/students': [\n        'MANAGER',\n        'TEACHER',\n        'RECEPTION',\n        'ACADEMIC_MANAGER'\n    ],\n    '/api/groups': [\n        'MANAGER',\n        'TEACHER'\n    ],\n    '/api/enrollments': [\n        'MANAGER',\n        'RECEPTION'\n    ],\n    '/api/attendance': [\n        'MANAGER',\n        'TEACHER'\n    ],\n    '/api/assessments': [\n        'MANAGER',\n        'TEACHER',\n        'ACADEMIC_MANAGER'\n    ],\n    '/api/leads': [\n        'MANAGER',\n        'RECEPTION'\n    ],\n    '/api/courses': [\n        'MANAGER'\n    ]\n};\n// Public routes that don't require authentication\nconst publicRoutes = [\n    '/',\n    '/auth/signin',\n    '/auth/signup',\n    '/auth/error',\n    '/api/auth',\n    '/api/health',\n    '/api/leads',\n    '/api/auth/verify'\n];\n// Specific inter-server routes (more restrictive than wildcard)\nconst interServerRoutes = [\n    '/api/inter-server/health',\n    '/api/inter-server/auth/validate',\n    '/api/inter-server/users'\n];\nasync function middleware(request) {\n    const { pathname } = request.nextUrl;\n    // Skip middleware for static files and Next.js internals\n    if (pathname.startsWith('/_next') || pathname.startsWith('/static') || pathname.includes('.') || pathname.startsWith('/favicon')) {\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.next();\n    }\n    // Check if route is public\n    const isPublicRoute = publicRoutes.some((route)=>{\n        if (route === pathname) return true;\n        if (route.endsWith('*') && pathname.startsWith(route.slice(0, -1))) return true;\n        return false;\n    });\n    // Check if route is an allowed inter-server route\n    const isInterServerRoute = interServerRoutes.includes(pathname);\n    // Allow public routes and specific inter-server routes\n    if (isPublicRoute || isInterServerRoute) {\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.next();\n    }\n    // Get the token from the request\n    const token = await (0,next_auth_jwt__WEBPACK_IMPORTED_MODULE_1__.getToken)({\n        req: request,\n        secret: process.env.NEXTAUTH_SECRET\n    });\n    // Redirect to signin if no token\n    if (!token) {\n        const signInUrl = new URL('/auth/signin', request.url);\n        signInUrl.searchParams.set('callbackUrl', pathname);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.redirect(signInUrl);\n    }\n    // Check role-based access for protected routes\n    const userRole = token.role;\n    // Staff server: Only allow staff roles (RECEPTION, ACADEMIC_MANAGER, TEACHER, MANAGER)\n    const serverType = process.env.SERVER_TYPE || 'staff';\n    if (serverType === 'staff') {\n        const allowedRoles = [\n            'RECEPTION',\n            'ACADEMIC_MANAGER',\n            'TEACHER',\n            'MANAGER'\n        ];\n        if (!allowedRoles.includes(userRole)) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.redirect(new URL('/auth/signin?error=unauthorized', request.url));\n        }\n    }\n    // Check dashboard routes\n    for (const [route, allowedRoles] of Object.entries(protectedRoutes)){\n        if (pathname.startsWith(route)) {\n            if (!allowedRoles.includes(userRole)) {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.redirect(new URL('/dashboard/unauthorized', request.url));\n            }\n            break;\n        }\n    }\n    // Check API routes\n    for (const [route, allowedRoles] of Object.entries(protectedApiRoutes)){\n        if (pathname.startsWith(route)) {\n            if (!allowedRoles.includes(userRole)) {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: 'Unauthorized access'\n                }, {\n                    status: 403\n                });\n            }\n            break;\n        }\n    }\n    // Special handling for student access\n    if (userRole === 'STUDENT') {\n        // Students can only access their own data\n        const userId = token.sub;\n        // Allow access to student dashboard\n        if (pathname.startsWith('/dashboard/student')) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.next();\n        }\n        // Restrict access to other dashboard routes\n        if (pathname.startsWith('/dashboard') && pathname !== '/dashboard') {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.redirect(new URL('/dashboard/student', request.url));\n        }\n    }\n    // Special handling for academic manager access\n    if (userRole === 'ACADEMIC_MANAGER') {\n        // Academic managers have access to assessments and test statistics\n        const allowedPaths = [\n            '/dashboard',\n            '/dashboard/assessments',\n            '/dashboard/students'\n        ];\n        const isAllowed = allowedPaths.some((path)=>pathname.startsWith(path));\n        if (!isAllowed && pathname.startsWith('/dashboard')) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.redirect(new URL('/dashboard/assessments', request.url));\n        }\n    }\n    // Teacher-specific restrictions\n    if (userRole === 'TEACHER') {\n        // Teachers can access their assigned groups and students\n        if (pathname.startsWith('/dashboard/teacher')) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.next();\n        }\n    }\n    // Reception-specific restrictions\n    if (userRole === 'RECEPTION') {\n        // Reception can access leads, students, and enrollments\n        const allowedPaths = [\n            '/dashboard',\n            '/dashboard/leads',\n            '/dashboard/students',\n            '/dashboard/enrollments'\n        ];\n        if (!allowedPaths.some((path)=>pathname.startsWith(path))) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.redirect(new URL('/dashboard', request.url));\n        }\n    }\n    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.next();\n}\nconst config = {\n    matcher: [\n        /*\r\n     * Match all request paths except for the ones starting with:\r\n     * - api/auth (NextAuth.js routes)\r\n     * - _next/static (static files)\r\n     * - _next/image (image optimization files)\r\n     * - favicon.ico (favicon file)\r\n     * - public folder\r\n     */ '/((?!api/auth|_next/static|_next/image|favicon.ico|public).*)'\n    ]\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(middleware)/./middleware.ts\n");

/***/ })

});