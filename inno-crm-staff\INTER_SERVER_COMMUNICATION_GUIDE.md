# Inter-Server Communication Guide

This guide explains how the Admin and Staff servers communicate securely with each other.

## 🏗️ Architecture Overview

```
┌─────────────────┐    Secure API    ┌─────────────────┐
│   Admin Server  │ ←──────────────→ │   Staff Server  │
│                 │                  │                 │
│ • Full Access   │                  │ • Limited Access│
│ • ADMIN/CASHIER │                  │ • STAFF Roles   │
│ • Financial Data│                  │ • Daily Ops     │
└─────────────────┘                  └─────────────────┘
```

## 🔐 Security Model

### Authentication
- **Shared Secret**: Both servers use `INTER_SERVER_SECRET` for authentication
- **Header-based Auth**: `X-Inter-Server-Secret` header validates requests
- **Server Identification**: `User-Agent` header identifies requesting server

### Authorization
- **Role-based Access**: Staff server can only access staff-role users
- **Endpoint Protection**: Inter-server endpoints require authentication
- **Request Validation**: All requests are logged and validated

## 📡 Communication Endpoints

### Health Check
```
GET /api/inter-server/health
```
- Checks server status and connectivity
- Returns server information and health status
- Used for monitoring and diagnostics

### User Authentication
```
POST /api/inter-server/auth/validate
Body: { phone: string, password: string }
```
- Staff server validates users against admin database
- Returns user data if authentication successful
- Enforces role-based access control

### Data Synchronization
```
POST /api/inter-server/sync
Body: { type: string, data: object, operation: string }
```
- Synchronizes data between servers
- Supports: users, students, leads, enrollments, attendance
- Operations: create, update, sync (upsert)

## 🔧 Implementation Details

### Library Functions

#### Making Requests
```typescript
import { makeInterServerRequest } from '@/lib/inter-server';

// Example: Staff server requesting user validation
const response = await makeInterServerRequest('admin', {
  endpoint: '/api/inter-server/auth/validate',
  method: 'POST',
  data: { phone: '+998901234567', password: 'password123' }
});
```

#### Validating Requests
```typescript
import { validateInterServerAuth } from '@/lib/inter-server';

// In API route
if (!validateInterServerAuth(request)) {
  return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
}
```

### Staff Server Usage
```typescript
import { StaffServerAPI } from '@/lib/inter-server';

// Authenticate user against admin server
const authResult = await StaffServerAPI.authenticateUser(phone, password);

// Get user data from admin server
const userData = await StaffServerAPI.getUserData(userId);

// Sync data with admin server
const syncResult = await StaffServerAPI.syncData('student', studentData);
```

### Admin Server Usage
```typescript
import { AdminServerAPI } from '@/lib/inter-server';

// Send data to staff server
const result = await AdminServerAPI.sendToStaff('/api/updates', data);

// Broadcast update to staff server
const broadcast = await AdminServerAPI.broadcastUpdate('user_updated', userData);
```

## 🚀 Setup Instructions

### 1. Environment Variables
Both servers need these variables:
```env
INTER_SERVER_SECRET="your-shared-secret-key"
ADMIN_SERVER_URL="https://inno-crm-admin.vercel.app"
STAFF_SERVER_URL="https://inno-crm-staff.vercel.app"
SERVER_TYPE="admin" # or "staff"
```

### 2. CORS Configuration
Update `vercel.json` to allow cross-origin requests:
```json
{
  "headers": [
    {
      "source": "/api/inter-server/(.*)",
      "headers": [
        {
          "key": "Access-Control-Allow-Origin",
          "value": "https://your-other-server.vercel.app"
        }
      ]
    }
  ]
}
```

### 3. API Routes
Copy the inter-server API routes to both servers:
- `/api/inter-server/health/route.ts`
- `/api/inter-server/auth/validate/route.ts`
- `/api/inter-server/sync/route.ts`

### 4. Library Integration
Copy `lib/inter-server.ts` to both servers and import as needed.

## 🧪 Testing Communication

### Health Check Test
```bash
# Test from staff server to admin server
curl -X GET https://inno-crm-admin.vercel.app/api/inter-server/health \
  -H "X-Inter-Server-Secret: your-secret" \
  -H "User-Agent: staff-server"
```

### Authentication Test
```bash
# Test user validation
curl -X POST https://inno-crm-admin.vercel.app/api/inter-server/auth/validate \
  -H "Content-Type: application/json" \
  -H "X-Inter-Server-Secret: your-secret" \
  -H "User-Agent: staff-server" \
  -d '{"phone":"+998901234567","password":"password123"}'
```

### Sync Test
```bash
# Test data synchronization
curl -X POST https://inno-crm-admin.vercel.app/api/inter-server/sync \
  -H "Content-Type: application/json" \
  -H "X-Inter-Server-Secret: your-secret" \
  -H "User-Agent: staff-server" \
  -d '{"type":"student","operation":"sync","data":{"id":"123","name":"Test"}}'
```

## 📊 Monitoring and Logging

### Request Logging
All inter-server requests are automatically logged with:
- Timestamp
- Direction (incoming/outgoing)
- Endpoint
- Success status
- Additional details

### Health Monitoring
```typescript
import { InterServerUtils } from '@/lib/inter-server';

// Check if admin server is reachable
const isAdminHealthy = await InterServerUtils.healthCheck('admin');

// Check if staff server is reachable
const isStaffHealthy = await InterServerUtils.healthCheck('staff');
```

### Error Handling
```typescript
const response = await makeInterServerRequest('admin', request);

if (!response.success) {
  console.error('Inter-server request failed:', response.error);
  // Handle error appropriately
}
```

## 🔄 Data Flow Examples

### User Authentication Flow
1. User logs in to staff server
2. Staff server sends credentials to admin server
3. Admin server validates against its database
4. Admin server returns user data if valid
5. Staff server creates session for user

### Data Synchronization Flow
1. Staff server creates/updates data
2. Staff server sends sync request to admin server
3. Admin server updates its database
4. Admin server confirms sync completion
5. Staff server logs sync status

### Real-time Updates Flow
1. Admin server updates critical data
2. Admin server broadcasts update to staff server
3. Staff server receives and processes update
4. Staff server updates its local cache/state
5. Staff server notifies connected users

## 🚨 Troubleshooting

### Common Issues

#### Authentication Failures
- Check `INTER_SERVER_SECRET` matches on both servers
- Verify `X-Inter-Server-Secret` header is included
- Ensure server URLs are correct

#### CORS Errors
- Update `vercel.json` with correct domains
- Check `Access-Control-Allow-Origin` headers
- Verify request methods are allowed

#### Connection Timeouts
- Check server availability
- Verify network connectivity
- Monitor Vercel function limits

### Debugging Tools
```typescript
// Get server configuration
const config = InterServerUtils.getServerConfig();
console.log('Server config:', config);

// Test connectivity
const canReachAdmin = await InterServerUtils.healthCheck('admin');
const canReachStaff = await InterServerUtils.healthCheck('staff');
```

## 📝 Best Practices

### Security
- Rotate `INTER_SERVER_SECRET` regularly
- Use HTTPS for all communications
- Log all inter-server requests
- Validate all incoming data

### Performance
- Cache frequently accessed data
- Use batch operations when possible
- Monitor request latency
- Implement retry logic for failed requests

### Reliability
- Handle network failures gracefully
- Implement circuit breaker pattern
- Use health checks for monitoring
- Set appropriate timeouts

### Maintenance
- Monitor inter-server logs
- Test communication after deployments
- Update both servers simultaneously
- Document any changes to API contracts
