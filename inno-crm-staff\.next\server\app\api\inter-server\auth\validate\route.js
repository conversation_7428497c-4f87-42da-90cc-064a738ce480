/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/inter-server/auth/validate/route";
exports.ids = ["app/api/inter-server/auth/validate/route"];
exports.modules = {

/***/ "(rsc)/./app/api/inter-server/auth/validate/route.ts":
/*!*****************************************************!*\
  !*** ./app/api/inter-server/auth/validate/route.ts ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_inter_server__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/inter-server */ \"(rsc)/./lib/inter-server.ts\");\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! bcryptjs */ \"bcryptjs\");\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(bcryptjs__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _lib_prisma__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/prisma */ \"(rsc)/./lib/prisma.ts\");\n// Inter-Server Authentication Validation Endpoint\n// Allows staff server to validate users against admin server\n\n\n\n\nasync function POST(request) {\n    try {\n        // Validate inter-server authentication\n        if (!(0,_lib_inter_server__WEBPACK_IMPORTED_MODULE_1__.validateInterServerAuth)(request)) {\n            _lib_inter_server__WEBPACK_IMPORTED_MODULE_1__.InterServerUtils.logRequest('incoming', '/api/inter-server/auth/validate', false, 'Unauthorized');\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Unauthorized inter-server request'\n            }, {\n                status: 401\n            });\n        }\n        const body = await request.json();\n        const { phone, password } = body;\n        if (!phone || !password) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Phone and password are required'\n            }, {\n                status: 400\n            });\n        }\n        // Find user in database\n        const user = await _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.user.findUnique({\n            where: {\n                phone\n            },\n            select: {\n                id: true,\n                name: true,\n                phone: true,\n                email: true,\n                role: true,\n                password: true,\n                createdAt: true,\n                updatedAt: true\n            }\n        });\n        if (!user) {\n            _lib_inter_server__WEBPACK_IMPORTED_MODULE_1__.InterServerUtils.logRequest('incoming', '/api/inter-server/auth/validate', false, 'User not found');\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Invalid credentials'\n            }, {\n                status: 401\n            });\n        }\n        // Verify password\n        const isValidPassword = await bcryptjs__WEBPACK_IMPORTED_MODULE_2___default().compare(password, user.password);\n        if (!isValidPassword) {\n            _lib_inter_server__WEBPACK_IMPORTED_MODULE_1__.InterServerUtils.logRequest('incoming', '/api/inter-server/auth/validate', false, 'Invalid password');\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Invalid credentials'\n            }, {\n                status: 401\n            });\n        }\n        // Check if user role is allowed on requesting server\n        const requestingServer = request.headers.get('User-Agent')?.includes('staff') ? 'staff' : 'admin';\n        const staffRoles = [\n            'RECEPTION',\n            'ACADEMIC_MANAGER',\n            'TEACHER',\n            'MANAGER',\n            'STUDENT'\n        ];\n        const adminRoles = [\n            'ADMIN',\n            'CASHIER'\n        ];\n        if (requestingServer === 'staff' && !staffRoles.includes(user.role)) {\n            _lib_inter_server__WEBPACK_IMPORTED_MODULE_1__.InterServerUtils.logRequest('incoming', '/api/inter-server/auth/validate', false, 'Role not allowed on staff server');\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Access denied for this server'\n            }, {\n                status: 403\n            });\n        }\n        // Return user data (excluding password)\n        const { password: _, ...userWithoutPassword } = user;\n        _lib_inter_server__WEBPACK_IMPORTED_MODULE_1__.InterServerUtils.logRequest('incoming', '/api/inter-server/auth/validate', true, `User ${user.id} validated`);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            user: userWithoutPassword\n        });\n    } catch (error) {\n        console.error('Inter-server auth validation error:', error);\n        _lib_inter_server__WEBPACK_IMPORTED_MODULE_1__.InterServerUtils.logRequest('incoming', '/api/inter-server/auth/validate', false, error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Authentication validation failed',\n            details: error instanceof Error ? error.message : 'Unknown error'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/inter-server/auth/validate/route.ts\n");

/***/ }),

/***/ "(rsc)/./lib/inter-server.ts":
/*!*****************************!*\
  !*** ./lib/inter-server.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AdminServerAPI: () => (/* binding */ AdminServerAPI),\n/* harmony export */   InterServerUtils: () => (/* binding */ InterServerUtils),\n/* harmony export */   StaffServerAPI: () => (/* binding */ StaffServerAPI),\n/* harmony export */   createInterServerHeaders: () => (/* binding */ createInterServerHeaders),\n/* harmony export */   makeInterServerRequest: () => (/* binding */ makeInterServerRequest),\n/* harmony export */   validateInterServerAuth: () => (/* binding */ validateInterServerAuth),\n/* harmony export */   withInterServerAuth: () => (/* binding */ withInterServerAuth)\n/* harmony export */ });\n// Inter-Server Communication Library\n// Handles secure communication between admin and staff servers\n/**\n * Validates inter-server request authentication\n */ function validateInterServerAuth(request) {\n    const authHeader = request.headers.get('X-Inter-Server-Secret');\n    const timestampHeader = request.headers.get('X-Timestamp');\n    const expectedSecret = process.env.INTER_SERVER_SECRET;\n    if (!authHeader || !expectedSecret) {\n        return false;\n    }\n    // Validate secret\n    if (authHeader !== expectedSecret) {\n        return false;\n    }\n    // Validate timestamp (prevent replay attacks)\n    if (timestampHeader) {\n        const requestTime = parseInt(timestampHeader);\n        const currentTime = Date.now();\n        const maxAge = 5 * 60 * 1000; // 5 minutes\n        if (isNaN(requestTime) || currentTime - requestTime > maxAge) {\n            console.warn('Inter-server request rejected: timestamp too old or invalid');\n            return false;\n        }\n    }\n    return true;\n}\n/**\n * Creates authenticated headers for inter-server requests\n */ function createInterServerHeaders() {\n    const secret = process.env.INTER_SERVER_SECRET;\n    if (!secret) {\n        throw new Error('INTER_SERVER_SECRET not configured');\n    }\n    const timestamp = Date.now().toString();\n    const serverConfig = InterServerUtils.getServerConfig();\n    const requestId = `${serverConfig.serverType}-${timestamp}-${Math.random().toString(36).substr(2, 9)}`;\n    return {\n        'Content-Type': 'application/json',\n        'X-Inter-Server-Secret': secret,\n        'X-Source-Server': serverConfig.serverType,\n        'X-Request-ID': requestId,\n        'X-Timestamp': timestamp,\n        'User-Agent': `${serverConfig.serverType}-server`\n    };\n}\n/**\n * Makes authenticated request to another server\n */ async function makeInterServerRequest(targetServer, request) {\n    try {\n        const baseUrl = targetServer === 'admin' ? process.env.ADMIN_SERVER_URL : process.env.STAFF_SERVER_URL;\n        if (!baseUrl) {\n            throw new Error(`${targetServer.toUpperCase()}_SERVER_URL not configured`);\n        }\n        const url = `${baseUrl}${request.endpoint}`;\n        const headers = {\n            ...createInterServerHeaders(),\n            ...request.headers\n        };\n        const response = await fetch(url, {\n            method: request.method,\n            headers,\n            body: request.data ? JSON.stringify(request.data) : undefined\n        });\n        const responseData = await response.json();\n        return {\n            success: response.ok,\n            data: responseData,\n            status: response.status,\n            error: response.ok ? undefined : responseData.error || 'Request failed'\n        };\n    } catch (error) {\n        return {\n            success: false,\n            status: 500,\n            error: error instanceof Error ? error.message : 'Unknown error'\n        };\n    }\n}\n/**\n * Middleware for protecting inter-server endpoints\n */ function withInterServerAuth(handler) {\n    return async (request, ...args)=>{\n        if (!validateInterServerAuth(request)) {\n            return new Response(JSON.stringify({\n                error: 'Unauthorized inter-server request'\n            }), {\n                status: 401,\n                headers: {\n                    'Content-Type': 'application/json'\n                }\n            });\n        }\n        return handler(request, ...args);\n    };\n}\n/**\n * Staff server functions - for requesting data from admin server\n */ class StaffServerAPI {\n    /**\n   * Request user authentication from admin server\n   */ static async authenticateUser(phone, password) {\n        return makeInterServerRequest('admin', {\n            endpoint: '/api/inter-server/auth/validate',\n            method: 'POST',\n            data: {\n                phone,\n                password\n            }\n        });\n    }\n    /**\n   * Get user data from admin server\n   */ static async getUserData(userId) {\n        return makeInterServerRequest('admin', {\n            endpoint: `/api/inter-server/users/${userId}`,\n            method: 'GET'\n        });\n    }\n    /**\n   * Sync data with admin server\n   */ static async syncData(dataType, data) {\n        return makeInterServerRequest('admin', {\n            endpoint: '/api/inter-server/sync',\n            method: 'POST',\n            data: {\n                type: dataType,\n                data\n            }\n        });\n    }\n}\n/**\n * Admin server functions - for handling requests from staff server\n */ class AdminServerAPI {\n    /**\n   * Validate staff server request\n   */ static async validateStaffRequest(request) {\n        return validateInterServerAuth(request);\n    }\n    /**\n   * Send data to staff server\n   */ static async sendToStaff(endpoint, data) {\n        return makeInterServerRequest('staff', {\n            endpoint,\n            method: 'POST',\n            data\n        });\n    }\n    /**\n   * Broadcast update to staff server\n   */ static async broadcastUpdate(updateType, data) {\n        return makeInterServerRequest('staff', {\n            endpoint: '/api/inter-server/updates',\n            method: 'POST',\n            data: {\n                type: updateType,\n                data\n            }\n        });\n    }\n}\n/**\n * Common utilities for both servers\n */ class InterServerUtils {\n    /**\n   * Log inter-server communication\n   */ static logRequest(direction, endpoint, success, details) {\n        const timestamp = new Date().toISOString();\n        const serverType = process.env.SERVER_TYPE || 'unknown';\n        console.log(`[${timestamp}] Inter-Server ${direction.toUpperCase()}: ${endpoint}`, {\n            server: serverType,\n            success,\n            details\n        });\n    }\n    /**\n   * Check if current server can communicate with target server\n   */ static async healthCheck(targetServer) {\n        try {\n            const response = await makeInterServerRequest(targetServer, {\n                endpoint: '/api/health',\n                method: 'GET'\n            });\n            return response.success;\n        } catch (error) {\n            return false;\n        }\n    }\n    /**\n   * Get server configuration\n   */ static getServerConfig() {\n        return {\n            serverType: process.env.SERVER_TYPE,\n            adminUrl: process.env.ADMIN_SERVER_URL,\n            staffUrl: process.env.STAFF_SERVER_URL,\n            hasInterServerSecret: !!process.env.INTER_SERVER_SECRET\n        };\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/inter-server.ts\n");

/***/ }),

/***/ "(rsc)/./lib/prisma.ts":
/*!***********************!*\
  !*** ./lib/prisma.ts ***!
  \***********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   prisma: () => (/* binding */ prisma)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n\nconst globalForPrisma = globalThis;\nconst prisma = globalForPrisma.prisma ?? new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient();\nif (true) globalForPrisma.prisma = prisma;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9saWIvcHJpc21hLnRzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUE2QztBQUU3QyxNQUFNQyxrQkFBa0JDO0FBSWpCLE1BQU1DLFNBQVNGLGdCQUFnQkUsTUFBTSxJQUFJLElBQUlILHdEQUFZQSxHQUFFO0FBRWxFLElBQUlJLElBQXFDLEVBQUVILGdCQUFnQkUsTUFBTSxHQUFHQSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxXaW5kb3dzIDExXFxEZXNrdG9wXFxjb2Rlc1xcaW5uby1jcm1cXGlubm8tY3JtLXN0YWZmXFxsaWJcXHByaXNtYS50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBQcmlzbWFDbGllbnQgfSBmcm9tICdAcHJpc21hL2NsaWVudCdcclxuXHJcbmNvbnN0IGdsb2JhbEZvclByaXNtYSA9IGdsb2JhbFRoaXMgYXMgdW5rbm93biBhcyB7XHJcbiAgcHJpc21hOiBQcmlzbWFDbGllbnQgfCB1bmRlZmluZWRcclxufVxyXG5cclxuZXhwb3J0IGNvbnN0IHByaXNtYSA9IGdsb2JhbEZvclByaXNtYS5wcmlzbWEgPz8gbmV3IFByaXNtYUNsaWVudCgpXHJcblxyXG5pZiAocHJvY2Vzcy5lbnYuTk9ERV9FTlYgIT09ICdwcm9kdWN0aW9uJykgZ2xvYmFsRm9yUHJpc21hLnByaXNtYSA9IHByaXNtYVxyXG4iXSwibmFtZXMiOlsiUHJpc21hQ2xpZW50IiwiZ2xvYmFsRm9yUHJpc21hIiwiZ2xvYmFsVGhpcyIsInByaXNtYSIsInByb2Nlc3MiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./lib/prisma.ts\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Finter-server%2Fauth%2Fvalidate%2Froute&page=%2Fapi%2Finter-server%2Fauth%2Fvalidate%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Finter-server%2Fauth%2Fvalidate%2Froute.ts&appDir=C%3A%5CUsers%5CWindows%2011%5CDesktop%5Ccodes%5Cinno-crm%5Cinno-crm-staff%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CWindows%2011%5CDesktop%5Ccodes%5Cinno-crm%5Cinno-crm-staff&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Finter-server%2Fauth%2Fvalidate%2Froute&page=%2Fapi%2Finter-server%2Fauth%2Fvalidate%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Finter-server%2Fauth%2Fvalidate%2Froute.ts&appDir=C%3A%5CUsers%5CWindows%2011%5CDesktop%5Ccodes%5Cinno-crm%5Cinno-crm-staff%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CWindows%2011%5CDesktop%5Ccodes%5Cinno-crm%5Cinno-crm-staff&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_Windows_11_Desktop_codes_inno_crm_inno_crm_staff_app_api_inter_server_auth_validate_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/inter-server/auth/validate/route.ts */ \"(rsc)/./app/api/inter-server/auth/validate/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/inter-server/auth/validate/route\",\n        pathname: \"/api/inter-server/auth/validate\",\n        filename: \"route\",\n        bundlePath: \"app/api/inter-server/auth/validate/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\inno-crm\\\\inno-crm-staff\\\\app\\\\api\\\\inter-server\\\\auth\\\\validate\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_Windows_11_Desktop_codes_inno_crm_inno_crm_staff_app_api_inter_server_auth_validate_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Finter-server%2Fauth%2Fvalidate%2Froute&page=%2Fapi%2Finter-server%2Fauth%2Fvalidate%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Finter-server%2Fauth%2Fvalidate%2Froute.ts&appDir=C%3A%5CUsers%5CWindows%2011%5CDesktop%5Ccodes%5Cinno-crm%5Cinno-crm-staff%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CWindows%2011%5CDesktop%5Ccodes%5Cinno-crm%5Cinno-crm-staff&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("@prisma/client");

/***/ }),

/***/ "bcryptjs":
/*!***************************!*\
  !*** external "bcryptjs" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("bcryptjs");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Finter-server%2Fauth%2Fvalidate%2Froute&page=%2Fapi%2Finter-server%2Fauth%2Fvalidate%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Finter-server%2Fauth%2Fvalidate%2Froute.ts&appDir=C%3A%5CUsers%5CWindows%2011%5CDesktop%5Ccodes%5Cinno-crm%5Cinno-crm-staff%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CWindows%2011%5CDesktop%5Ccodes%5Cinno-crm%5Cinno-crm-staff&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();